const { getDefaultConfig } = require("@expo/metro-config");
const path = require("path");
const fs = require("fs");

const projectRoot = __dirname;
const workspaceRoot = path.resolve(projectRoot, "../../");

const config = getDefaultConfig(projectRoot);

// Check if we're in EAS build environment
const isEASBuild = process.env.EAS_BUILD === "true" || process.env.CI === "true";

console.log(`Metro config: ${isEASBuild ? 'EAS Build' : 'Local Development'} mode`);

// Define package paths based on environment
const getPackagePaths = () => {
  if (isEASBuild) {
    // In EAS build, packages are copied to apps/mobile/packages/
    return {
      backend: path.resolve(projectRoot, "packages/backend"),
      lib: path.resolve(projectRoot, "packages/lib"),
      assets: path.resolve(projectRoot, "packages/assets"),
      localConvex: path.resolve(projectRoot, "convex")
    };
  } else {
    // In local development, use workspace packages
    return {
      backend: path.resolve(workspaceRoot, "packages/backend"),
      lib: path.resolve(workspaceRoot, "packages/lib"),
      assets: path.resolve(workspaceRoot, "packages/assets"),
      localConvex: path.resolve(projectRoot, "convex")
    };
  }
};

const packagePaths = getPackagePaths();

// Check if backend package exists, if not use local convex
const useLocalConvex = !fs.existsSync(packagePaths.backend) ||
                      fs.existsSync(path.join(packagePaths.localConvex, "_generated"));

console.log('Package paths:', {
  backend: packagePaths.backend,
  lib: packagePaths.lib,
  assets: packagePaths.assets,
  useLocalConvex,
});

// Set up watch folders
if (isEASBuild) {
  // In EAS build, only watch what's available locally
  config.watchFolders = [projectRoot];

  // Add package folders if they exist
  Object.entries(packagePaths).forEach(([name, pkgPath]) => {
    if (name !== 'localConvex' && fs.existsSync(pkgPath)) {
      config.watchFolders.push(pkgPath);
      console.log(`Watching ${name} package at: ${pkgPath}`);
    }
  });
} else {
  // Local development - watch workspace root and packages
  config.watchFolders = [workspaceRoot];

  // Add individual packages that exist
  if (fs.existsSync(packagePaths.lib)) {
    config.watchFolders.push(packagePaths.lib);
  }
  if (fs.existsSync(packagePaths.assets)) {
    config.watchFolders.push(packagePaths.assets);
  }
  if (!useLocalConvex && fs.existsSync(packagePaths.backend)) {
    config.watchFolders.push(packagePaths.backend);
  }
}

// Map the aliases to the actual package locations
config.resolver.extraNodeModules = {
  "@workspace/backend": useLocalConvex ? packagePaths.localConvex : packagePaths.backend,
  "@workspace/lib": packagePaths.lib,
  "@workspace/assets": packagePaths.assets,
};

// Set up alias resolution for assets and workspace packages
config.resolver.alias = {
  // Asset aliases to match tsconfig.json paths
  "@default-avatar.svg": path.resolve(projectRoot, "assets/icons/default-avatar.svg"),
  "@icon.png": path.resolve(projectRoot, "assets/icon.png"),
  "@assets": path.resolve(projectRoot, "assets"),
};

// Fix the alias resolution for EAS builds
if (isEASBuild && useLocalConvex) {
  config.resolver.alias = {
    ...config.resolver.alias,
    "@workspace/backend/convex/_generated/api": path.resolve(packagePaths.localConvex, "_generated/api"),
    "@workspace/backend/convex/_generated/dataModel": path.resolve(packagePaths.localConvex, "_generated/dataModel"),
    "@workspace/backend/convex/_generated/server": path.resolve(packagePaths.localConvex, "_generated/server"),
  };
}

// Set up node modules paths
config.resolver.nodeModulesPaths = [
  path.resolve(projectRoot, 'node_modules'),
];

// In local development, also include workspace node_modules
if (!isEASBuild) {
  config.resolver.nodeModulesPaths.unshift(path.resolve(workspaceRoot, 'node_modules'));
}

// Enable symlinks resolution for local development
if (!isEASBuild) {
  config.resolver.resolveRequest = (context, moduleName, platform) => {
    // Let Metro handle workspace packages
    if (moduleName.startsWith('@workspace/')) {
      return context.resolveRequest(context, moduleName, platform);
    }
    return context.resolveRequest(context, moduleName, platform);
  };
}

console.log('Metro config setup complete');
console.log('Watch folders:', config.watchFolders);
console.log('Extra node modules:', config.resolver.extraNodeModules);

module.exports = config;